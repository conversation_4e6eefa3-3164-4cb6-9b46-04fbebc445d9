---
interface Props {
  title: string;
}

import "clientgiant-styles/styles";
import "../styles/styles.css";

import Footer from "../components/Footer.astro";
import Header from "../components/Header.astro";
import MobileNav from "../components/MobileNav.astro";
import SalesDemo from "../components/SalesDemo.astro";

const { title } = Astro.props;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>

    <!--TODO: probably use all.min.css & fonts instead-->
    <script
      is:inline
      src="https://kit.fontawesome.com/0393669796.js"
      crossorigin="anonymous"></script>

    <script
      src="https://flackr.github.io/scroll-timeline/dist/scroll-timeline.js"
    ></script>
  </head>

  <body>
    <SalesDemo />
    <Header />
    <MobileNav />
    <main>
      <slot />
    </main>
    <Footer />
  </body>
</html>
