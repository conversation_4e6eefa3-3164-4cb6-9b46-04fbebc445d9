import type { BORDER_RADIUS } from "./border-radius.type";
import type { SPACE_SIZES } from "./spaces.type";
import type { SURFACES } from "./surface.type";

export interface BOX_PROPS {
  surface?: null | SURFACES;
  borderRadius?: BORDER_RADIUS;
  padding?: null | SPACE_SIZES;
  paddingInline?: null | SPACE_SIZES;
  paddingBlock?: null | SPACE_SIZES;
  paddingBlockStart?: null | SPACE_SIZES;
  paddingBlockEnd?: null | SPACE_SIZES;
  paddingInlineStart?: null | SPACE_SIZES;
  paddingInlineEnd?: null | SPACE_SIZES;
  class?: string;
}
