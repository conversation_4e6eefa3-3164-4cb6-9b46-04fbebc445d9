:root {
    --fw-thin: var(--cg-font-weight-100);
    --fw-extra-light: var(--cg-font-weight-200);
    --fw-light: var(--cg-font-weight-300);
    --fw-regular: var(--cg-font-weight-400);
    --fw-medium: var(--cg-font-weight-500);
    --fw-semi-bold: var(--cg-font-weight-600);
    --fw-bold: var(--cg-font-weight-700);
    --fw-extra-bold: var(--cg-font-weight-800);
    --fw-black: var(--cg-font-weight-900);
}

[data-font-weight] {
    font-weight: var(--cg-font-weight);
}

/* Weight */
[data-font-weight="Thin"] {
    --cg-font-weight: var(--fw-thin);
}
[data-font-weight="ExtraLight"] {
    --cg-font-weight: var(--fw-extra-light);
}
[data-font-weight="Light"] {
    --cg-font-weight: var(--fw-light);
}
[data-font-weight="Regular"] {
    --cg-font-weight: var(--fw-regular);
}
[data-font-weight="Medium"] {
    --cg-font-weight: var(--fw-medium);
}
[data-font-weight="SemiBold"] {
    --cg-font-weight: var(--fw-semi-bold);
}
[data-font-weight="Bold"] {
    --cg-font-weight: var(--fw-bold);
}
[data-font-weight="ExtraBold"] {
    --cg-font-weight: var(--fw-extra-bold);
}
[data-font-weight="Black"] {
    --cg-font-weight: var(--fw-black);
}

/* Size */
[data-font-size] {
    font-size: var(--cg-font-size);
}

[data-font-size="100"] {
    --cg-font-size: var(--cg-font-size-100);
}
[data-font-size="200"] {
    --cg-font-size: var(--cg-font-size-200);
}
[data-font-size="300"] {
    --cg-font-size: var(--cg-font-size-300);
}
[data-font-size="400"] {
    --cg-font-size: var(--cg-font-size-400);
}
[data-font-size="500"] {
    --cg-font-size: var(--cg-font-size-500);
}
[data-font-size="600"] {
    --cg-font-size: var(--cg-font-size-600);
}

[data-text-align="center"] {
    text-align: center;
}
