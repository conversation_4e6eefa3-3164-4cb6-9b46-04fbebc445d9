.hero {
    position: relative;
    --hero-shapes-border: calc(var(--cg-border-radius-400) * 2);
}
.hero section {
    display: flex;
    gap: var(--cg-space-300);
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
}
.hero .copy {
    margin-top: calc(var(--cg-space-600) * -1);
    z-index: 3;
}
.hero img {
    margin-bottom: calc(var(--cg-space-600) * -1);
    z-index: 5;
}
.hero-blob {
    position: absolute;
}
@media (min-width: 960px) {
    .hero section {
        flex-direction: row;
    }
    .hero .copy {
        margin-top: calc(var(--cg-space-900) * -1);
        z-index: 3;
    }
    .hero img {
        margin-bottom: calc(var(--cg-space-800) * -1);
    }
}
