---
import { Image } from "astro:assets";

import MainLayout from "../layouts/MainLayout.astro";
import Deck from "../components/Deck.astro";
import Card from "../components/Card.astro";
import Button from "../components/Button.astro";
import Box from "../components/Box.astro";
import Tile from "../components/Tile.astro";
import Stack from "../components/Stack.astro";
import Inline from "../components/Inline.astro";
import Video from "../components/Video.astro";
import Icon from "../components/Icon.astro";
import CommandCenterImg from "../components/custom/CommandCenterImg.astro";
import TestimonialChat from "../components/custom/TestimonialChat.astro";
import DotGrid from "../components/DotGrid.astro";

import HeroGirl from "../assets/images/HeroGirl.png";
import ReadyToLeave from "../assets/images/ready-to-leave.svg";
import <PERSON><PERSON><PERSON><PERSON> from "../assets/images/testimonial-Jinger-Liner.jpeg";
import <PERSON><PERSON><PERSON><PERSON> from "../assets/images/testimonial-<PERSON>-<PERSON><PERSON><PERSON>.webp";
import Greg<PERSON>ummings from "../assets/images/testimonial-<PERSON>-<PERSON>.jpg";

/* Trusted Brand Logos */
import Merrill from "../assets/svgs/trust-merrill.svg";
import Alkeme from "../assets/svgs/trust-alkeme.svg";
import Allstate from "../assets/svgs/trust-allstate.svg";
import LPL from "../assets/svgs/trust-lpl.svg";
import USBank from "../assets/svgs/trust-usbank.svg";
import Citi from "../assets/svgs/trust-citi.svg";
import EngelVolkers from "../assets/svgs/trust-engelvolkers.svg";
import FinanceofAmerica from "../assets/svgs/trust-foac.svg";
import LoanDepot from "../assets/svgs/trust-loandepot.svg";
import NewAmerican from "../assets/svgs/trust-newamerican.svg";
import OpenDoor from "../assets/svgs/trust-opendoor.svg";
import Redfin from "../assets/svgs/trust-redfin.svg";
import Salesforce from "../assets/svgs/trust-salesforce.svg";
import TomFerry from "../assets/svgs/trust-tomferry.svg";
import TruTeam from "../assets/svgs/trust-truteam.svg";
---

<script>
    import Splide from "@splidejs/splide";
    import "@splidejs/splide/css";

    document.addEventListener("DOMContentLoaded", function () {
        var splide = new Splide(".splide", {
            arrows: false,
            autoplay: true,
            breakpoints: {
                768: {
                    perPage: 3,
                },
            },
            interval: 5000,
            pagination: false,
            pauseOnHover: false,
            pauseOnFocus: false,
            perPage: 5,
            speed: 5000,
            type: "loop",
        });
        splide.mount();
    });
</script>

<style>
    .hero-blob.top-right-1 {
        border-radius: 0 var(--cg-border-radius-400) 0 var(--hero-shapes-border);
        top: 0;
        right: 0;
        width: 35%;
        height: 25%;
        background-color: var(--cg-neutral-100);
        opacity: 0.2;
    }
    .hero-blob.top-right-2 {
        border-radius: 0 var(--cg-border-radius-400) 0 var(--hero-shapes-border);
        top: 0;
        right: 0;
        width: 33%;
        height: 18%;
        background-color: var(--cg-neutral-100);
        opacity: 0.2;
    }
    .hero-blob.bottom-right {
        border-radius: var(--hero-shapes-border) 0 var(--cg-border-radius-400)
            var(--cg-border-radius-400);
        bottom: 0;
        right: 0;
        width: 30%;
        height: 36%;
        background-color: var(--cg-neutral-100);
        opacity: 0.5;
    }
    .hero-blob.bottom-left {
        border-radius: var(--cg-border-radius-400);
        border-top-right-radius: var(--hero-shapes-border);
        bottom: 0;
        left: calc(var(--cg-border-radius-400) * -1);
        width: 30%;
        height: 25%;
        background-color: var(--cg-neutral-100);
        opacity: 0.4;
    }

    /* Max width classes */
    .max-900 {
        max-width: 900px;
        margin-inline: auto;
    }
    .p-500 {
        max-width: 500px;
    }
    .p-900 {
        max-width: 900px;
    }

    /* Custom container classes */
    .focus-tiles {
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
        * > & {
            flex: 1;
        }
    }
    @media (min-width: 768px) {
        .focus-tiles {
            flex-direction: row;
        }
    }
    .stay-top-of-mind {
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
        * > & {
            flex: 1;
        }
    }
    @media (min-width: 768px) {
        .stay-top-of-mind {
            flex-direction: row-reverse;
            align-items: center;
            justify-content: space-between;
        }
    }

    .existing-relationships-video {
        display: grid;
        align-items: center;
        grid-template-columns: 1fr;
    }
    @media (min-width: 960px) {
        .existing-relationships-video {
            gap: var(--cg-space-500);
            grid-template-columns: 1fr 2fr;
        }
    }

    .features-button-links {
        display: flex;
        gap: var(--cg-space-300);
        flex-wrap: wrap;
        button.button-link {
            color: var(--cg-celeste-800);
            border-width: 0 0 3px 0;
            border-color: var(--cg-celeste-300);
            padding-inline: var(--cg-space-0);
            border-radius: var(--cg-border-radius-0);
            &.selected {
                border-bottom-color: var(--cg-celeste-500);
            }
        }
    }
    .features-deck {
        display: flex;
        gap: var(--cg-space-500);
        flex-direction: column;
        & > * {
            flex: 1;
        }
    }
    @media (min-width: 768px) {
        .features-deck {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }
    .features-list-parent {
        container-type: inline-size;
    }
    .features-list-item {
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
    }
    @container (min-width: 300px) {
        .features-list-item {
            align-items: center;
            flex-direction: row;
        }
    }

    .proven-results-parent {
        container-type: inline-size;
    }
    .proven-results {
        display: flex;
        gap: var(--cg-space-500);
        flex-direction: column;
    }
    .testimonial-icon {
        background-color: var(--cg-sunset-100);
        color: var(--cg-sunset-600);
    }
    @container (min-width: 480px) {
        .proven-results {
            align-items: center;
            flex-direction: row;
        }
    }
    .testimonials {
        display: grid;
        gap: var(--cg-space-500);
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        padding-inline: 0;
        & > .cg-stack {
            grid-template-rows: auto 1fr;
        }
    }
    .quote {
        margin-top: var(--cg-space-300);
        padding-left: var(--cg-space-300);
        border-left: 2px solid var(--cg-sunset-500);
        h4 {
            color: var(--cg-midnight-600);
        }
    }
    @media (min-width: 480px) {
        .testimonials {
            padding-inline: var(--cg-space-600);
        }
        .quote {
            padding-left: var(--cg-space-500);
        }
    }
    .trusted-brands {
        color: var(--cg-midnight-700);
    }
    .trusted-brands .splide__slide {
        display: flex;
        justify-items: center;
        align-items: center;
    }
</style>

<MainLayout title="Client Giant">
    <Deck class="surface surface-midnight-dark">
        <!--Hero-->
        <Card surface="celeste" class="hero">
            <section>
                <Box class="copy">
                    <Stack justify="stretch">
                        <h1 data-font-size="600" data-font-weight="Bold">
                            Supercharge Your Growth with Customer Care
                        </h1>
                        <ul class="unlist cg-stack" data-stack-size="200">
                            <li class="cg-inline" data-align="center">
                                <Icon
                                    size="300"
                                    fontSize="300"
                                    icon="fa-solid fa-circle-check"
                                />
                                Skyrocket referrals without lifting a finger
                            </li>
                            <li class="cg-inline" data-align="center">
                                <Icon
                                    size="300"
                                    fontSize="300"
                                    icon="fa-solid fa-circle-check"
                                />
                                Secure customer retention and reduce churn
                            </li>
                            <li class="cg-inline" data-align="center">
                                <Icon
                                    size="300"
                                    fontSize="300"
                                    icon="fa-solid fa-circle-check"
                                />
                                Keep your renewals on autopilot
                            </li>
                        </ul>
                        <Inline>
                            <Button label="Book a Demo" type="link" />
                            <Button
                                outline={true}
                                label="Get Started"
                                type="link"
                            />
                        </Inline>
                        <p>
                            The #1 automated engagement platform for client care
                            and employee happiness.
                        </p>
                    </Stack>
                </Box>
                <Image src={HeroGirl} alt="" width="500" height="500" />
            </section>
            <div class="hero-blob top-right-1"></div>
            <div class="hero-blob top-right-2"></div>
            <div class="hero-blob bottom-right"></div>
            <div class="hero-blob bottom-left"></div>
        </Card>

        <!--Focus, Top of Mind, Command Center-->
        <Card surface="white" stackOrder="2">
            <Stack size="700">
                <!--Focus-->
                <div class="focus-tiles">
                    <Tile surface="celeste-light">
                        <section>
                            <Stack justify="start">
                                <Icon size="auto" icon="fa-regular fa-heart" />

                                <h2 data-font-size="600">
                                    We focus on all of your people
                                </h2>
                                <p>
                                    Fostering meaningful relationships with your
                                    existing customers is your most powerful
                                    tool. Let us handle this for you, all
                                    automated and with minimal effort
                                </p></Stack
                            >
                        </section>
                    </Tile>
                    <Tile surface="celeste-light">
                        <section>
                            <Stack justify="start">
                                <Icon
                                    size="auto"
                                    icon="fa-regular fa-chart-line-up"
                                />
                                <h2 data-font-size="600">
                                    While you focus on your business
                                </h2>
                                <p>
                                    While your customers know they are
                                    top-of-mind with you, they feel at ease
                                    while your efforts are spent growing your
                                    business and gaining new customers.
                                </p>
                            </Stack>
                        </section>
                    </Tile>
                </div>
                <!--Top of Mind-->
                <section class="stay-top-of-mind">
                    <Box padding="0">
                        <ReadyToLeave />
                    </Box>
                    <Box>
                        <Stack justify="start">
                            <h2 data-font-size="600">
                                <span data-font-weight="Regular">Stay</span>
                                Top of Mind
                            </h2>
                            <p class="p-500">
                                Research shows that 68% of customers leave a
                                business due to lack of engagement or failure to
                                stay top-of-mind.
                            </p>
                            <Button
                                type="link"
                                label=" How We Solve This"
                                outline={true}
                                backIcon="fa-solid fa-arrow-right"
                            />
                        </Stack>
                    </Box>
                </section>
                <!--Command Center-->
                <section class="command-center">
                    <Box paddingBlockStart="0">
                        <div data-text-align="center" class="max-900">
                            <Stack>
                                <DotGrid color="midnight" center={true} />
                                <h2 data-font-size="500">
                                    We Are Your Command Center for Generating
                                    Referrals
                                </h2>
                                <p>
                                    <span data-font-weight="SemiBold">
                                        70% of customers
                                    </span>
                                    who feel companies are emotionally connected
                                    to them are likely to recommend that business
                                    to others. Our automated suite of tools helps
                                    you spread the word organically and authentically.
                                </p>
                            </Stack>
                        </div>
                    </Box>
                    <Box paddingInline="0">
                        <CommandCenterImg />
                    </Box>
                </section>
            </Stack>
        </Card>

        <!--Video -->
        <Card stackOrder="1">
            <section class="existing-relationships-video">
                <Box paddingInlineEnd="0" paddingBlockEnd="0">
                    <Stack justify="start">
                        <h2 data-font-size="500">
                            Why Your Existing Relationships Are Your Most
                            Valuable
                        </h2>
                        <p>
                            We all know what it feels like when someone makes us
                            feel special and appreciated. We're thankful and we
                            instinctively feel a reflex response to do something
                            nice in return.
                        </p>
                        <Button
                            type="link"
                            label="Book a Demo"
                            outline={true}
                        />
                    </Stack>
                </Box>
                <Box paddingInline="0">
                    <Video
                        video="https://player.vimeo.com/video/907601155?color=232937&title=0&byline=0&portrait=0"
                    />
                    <script
                        is:inline
                        src="https://player.vimeo.com/api/player.js"
                        data-ot-ignore="true"
                        defer="true"></script>
                </Box>
            </section>
        </Card>

        <!--Features Deck (Intelligent Gifting), Testimonials-->
        <Card surface="white" stackOrder="2">
            <Stack size="700">
                <!--Features Deck-->
                <Stack size="0">
                    <Box padding="300">
                        <div class="features-button-links">
                            <Button
                                class="selected -outline button-link"
                                type="button"
                                label="Intelligent Gifting"
                                frontIcon="fa-regular fa-gift"
                            />
                            <Button
                                class="-outline button-link"
                                type="button"
                                label="Learning Tools"
                                frontIcon="fa-regular fa-sparkles"
                            />
                            <Button
                                class="-outline button-link"
                                type="button"
                                label="Boost Social Ratings"
                                frontIcon="fa-regular fa-thumbs-up"
                            />
                            <Button
                                class="-outline button-link"
                                type="button"
                                label="Create Engagements"
                                frontIcon="fa-regular fa-qrcode"
                            />
                        </div>
                    </Box>
                    <Tile surface="celeste-light">
                        <section class="features-deck">
                            <Stack justify="start">
                                <Icon
                                    box={true}
                                    class="icon"
                                    icon="fa-light fa-gift"
                                />
                                <h2 data-font-size="500">
                                    Intelligent Gifting
                                </h2>
                                <p class="p-500">
                                    Cut through the noise of traditional
                                    marketing efforts and branded gifts. Our
                                    intelligent gifting solutions take into
                                    account thoughtful moments that are timed
                                    accordingly to fit your needs.
                                </p>
                                <div>
                                    <Button
                                        type="link"
                                        label="Book a Demo"
                                        outline={true}
                                    />
                                </div>
                            </Stack>
                            <div>
                                <ul
                                    class="unlist cg-stack features-list-parent"
                                >
                                    <li class="features-list-item">
                                        <Icon
                                            class="icon"
                                            size="1000"
                                            fontSize="600"
                                            icon="fa-light fa-share-nodes"
                                            box={true}
                                        />
                                        <Stack justify="start" size="100">
                                            <h3 data-font-size="400">
                                                Show Your Referrals You Care
                                            </h3>
                                            <div>
                                                <p>
                                                    Acknowledge those who are
                                                    helping spread the word with
                                                    thoughtful thank you's.
                                                </p>
                                            </div>
                                        </Stack>
                                    </li>
                                    <li class="features-list-item">
                                        <Icon
                                            class="icon"
                                            size="1000"
                                            fontSize="600"
                                            icon="fa-light fa-hand-wave"
                                            box={true}
                                        />
                                        <Stack justify="start" size="100">
                                            <h3 data-font-size="400">
                                                Welcome Your New Customers
                                            </h3>
                                            <div>
                                                <p>
                                                    Engage with new customers
                                                    and show them they are
                                                    valuable to turn them into
                                                    referrals.
                                                </p>
                                            </div>
                                        </Stack>
                                    </li>
                                    <li class="features-list-item">
                                        <Icon
                                            class="icon"
                                            size="1000"
                                            fontSize="600"
                                            icon="fa-light fa-handshake-angle"
                                            box={true}
                                        />
                                        <Stack justify="start" size="100">
                                            <h3 data-font-size="400">
                                                Strengthen Relationships
                                            </h3>
                                            <div>
                                                <p>
                                                    Keep track of who has
                                                    referred you the most
                                                    business to keep them
                                                    top-of-mind.
                                                </p>
                                            </div>
                                        </Stack>
                                    </li>
                                </ul>
                            </div>
                        </section>
                    </Tile>
                </Stack>

                <section>
                    <!--Proven Results-->
                    <Box>
                        <div class="proven-results-parent">
                            <div class="proven-results" data-inline-size="500">
                                <Icon
                                    size="1100"
                                    fontSize="800"
                                    icon="fa-solid fa-comment-quote"
                                    class="testimonial-icon"
                                />
                                <Stack justify="start">
                                    <h2 data-font-size="500">
                                        Proven Results from Our Members
                                    </h2>
                                    <p class="p-900">
                                        Client Giant proudly helps wealth
                                        managers, real-estate agents, and human
                                        resources teams across the world
                                        increase customer and employee
                                        retention. See some of our success
                                        stories below...
                                    </p>
                                </Stack>
                            </div>
                        </div>
                    </Box>
                    <!--Testimonials-->
                    <div class="testimonials">
                        <Stack size="200">
                            <TestimonialChat
                                image={JingerLiner}
                                copy="Increased Loyalty<br/>and Engagement"
                            />
                            <div class="quote">
                                <Stack justify="start" size="200">
                                    <h3 data-font-size="400">Jinger Liner</h3>
                                    <h4 data-font-weight="Medium">
                                        Director of Human Resources
                                    </h4>
                                    <p>
                                        “Client Giant has completely transformed
                                        how we show appreciation for our people.
                                        It takes the work off our plate while
                                        delivering meaningful, personalized
                                        moments that boost morale and
                                        engagement. It's like having a full-time
                                        happiness department. It's rare for
                                        something in our world to be able to
                                        show real ROI, but happier, more
                                        productive team members, increased
                                        loyalty, and being a place great people
                                        want to come work is priceless.”
                                    </p>
                                </Stack>
                            </div>
                        </Stack>
                        <Stack size="200">
                            <TestimonialChat
                                image={TimDunigan}
                                copy="+$50,000<br/>Revenue"
                            />
                            <div class="quote">
                                <Stack justify="start" size="200">
                                    <h3 data-font-size="400">Tim Dunigan</h3>
                                    <h4 data-font-weight="Medium">
                                        Wealth Management
                                    </h4>
                                    <p>
                                        “In Spring 2021, I signed up 5 clients
                                        for Top of Mind. Within 2 Cycles, I
                                        generated $50,000+ in revenue and added
                                        more than $3MM in AUM from additional
                                        business and referrals. In any business
                                        built on referrals or client retention,
                                        Client Giant is a no-brainer.”
                                    </p></Stack
                                >
                            </div>
                        </Stack>
                        <Stack size="200">
                            <TestimonialChat
                                image={GregCummings}
                                copy="+$200,000<br/>Commission"
                            />
                            <div class="quote">
                                <Stack justify="start" size="200">
                                    <h3 data-font-size="400">Greg Cummings</h3>
                                    <h4 data-font-weight="Medium">
                                        Real Estate
                                    </h4>
                                    <p>
                                        “I started with 30 people on Top of Mind
                                        and received 9 referrals within 60 days
                                        [equaling a potential of $200,000 in
                                        commission]. I made sure to add all of
                                        my clients after that. Top of Mind is a
                                        no brainer.”
                                    </p>
                                </Stack>
                            </div>
                        </Stack>
                    </div>
                </section>
            </Stack>
        </Card>
        <Card surface="midnight-light" stackOrder="1">
            <Box paddingBlockStart="1000" paddingBlockEnd="0">
                <section class="trusted-brands" data-text-align="center">
                    <Stack>
                        <h2 data-font-weight="SemiBold" data-font-size="400">
                            Driving Growth for the Most Trusted Brands
                        </h2>
                        <div
                            class="splide"
                            aria-label="Brands logos for companies that use Client Giant"
                        >
                            <div class="splide__track">
                                <ul class="splide__list">
                                    <li class="splide__slide"><Merrill /></li>
                                    <li class="splide__slide"><Alkeme /></li>
                                    <li class="splide__slide"><Allstate /></li>
                                    <li class="splide__slide"><LPL /></li>
                                    <li class="splide__slide"><USBank /></li>
                                    <li class="splide__slide"><TomFerry /></li>
                                    <li class="splide__slide">
                                        <Salesforce />
                                    </li>
                                    <li class="splide__slide"><Citi /></li>
                                    <li class="splide__slide">
                                        <EngelVolkers />
                                    </li>
                                    <li class="splide__slide">
                                        <FinanceofAmerica />
                                    </li>
                                    <li class="splide__slide">
                                        <LoanDepot />
                                    </li>
                                    <li class="splide__slide">
                                        <NewAmerican />
                                    </li><li class="splide__slide">
                                        <OpenDoor />
                                    </li>
                                    <li class="splide__slide">
                                        <Redfin />
                                    </li>
                                    <li class="splide__slide">
                                        <TruTeam />
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </Stack>
                </section>
            </Box>
        </Card>
    </Deck>
</MainLayout>
