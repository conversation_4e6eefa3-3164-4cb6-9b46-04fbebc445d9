---
import { Image } from "astro:assets";

import MainLayout from "../layouts/MainLayout.astro";
import Button from "../components/Button.astro";
import Card from "../components/Card.astro";
import Deck from "../components/Deck.astro";
import Stack from "../components/Stack.astro";
import Icon from "../components/Icon.astro";
import Box from "../components/Box.astro";
import Tile from "../components/Tile.astro";
import DotGrid from "../components/DotGrid.astro";
import Inline from "../components/Inline.astro";

import AutomatedGiftingHero from "../assets/images/AutomatedGiftingHero.png";
import iPhone from "../assets/images/iPhone.png";
import NotVerySatisfied from "../assets/images/not-very-satisfied.svg";
import TopOfMind from "../assets/svgs/duotone-top-of-mind.svg";
import SpecialMoments from "../assets/svgs/duotone-special-moments.svg";
import BulkGifting from "../assets/svgs/duotone-bulk-gifting.svg";
---

<script>
    import Splide from "@splidejs/splide";
    import "@splidejs/splide/css";

    document.addEventListener("DOMContentLoaded", function () {
        var giftSplide = new Splide(".gift-carousel", {
            pagination: false,
            perPage: 1,
            type: "loop",
        });
        var messagingSplide = new Splide(".messaging-carousel", {
            pagination: false,
            perPage: 1,
            type: "loop",
        });
        giftSplide.mount();
        messagingSplide.mount();
    });
</script>

<style>
    .hero-blob.top-right-1 {
        border-radius: 0 var(--cg-border-radius-400) 0 var(--hero-shapes-border);
        top: 0;
        right: 0;
        width: 50%;
        height: 50%;
        background-color: var(--cg-neutral-100);
        opacity: 0.2;
    }
    .hero-blob.top-right-2 {
        border-radius: 0 var(--cg-border-radius-400) 0 var(--hero-shapes-border);
        top: 0;
        right: 0;
        width: 40%;
        height: 20%;
        background-color: var(--cg-neutral-100);
        opacity: 0.2;
    }
    .hero-blob.bottom-left-1 {
        border-radius: var(--cg-border-radius-400);
        border-top-right-radius: var(--hero-shapes-border);
        bottom: 0;
        left: calc(var(--cg-border-radius-400) * -1);
        width: 44%;
        height: 30%;
        background-color: var(--cg-neutral-100);
        opacity: 0.5;
    }
    .hero-blob.bottom-left-2 {
        border-radius: var(--cg-border-radius-400);
        bottom: 0;
        left: calc(var(--cg-border-radius-400) * -1);
        width: 40%;
        height: 24%;
        background-color: var(--cg-neutral-100);
        opacity: 0.4;
    }

    .max-900 {
        max-width: 900px;
        margin-inline: auto;
    }
    .p-400 {
        max-width: 400px;
    }
    .p-600 {
        max-width: 600px;
    }
    .take-care-tiles {
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
        & > * {
            flex: 1;
        }
    }
    @media (min-width: 960px) {
        .take-care-tiles {
            flex-direction: row;
        }
    }
    .take-care-tile {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .duotone-icon {
            max-width: var(--cg-space-1000);
            margin: calc(var(--cg-space-500) * -1) auto 0;
        }
    }
    .take-care-bordered-tile {
        border: 5px solid var(--cg-midnight-400);
        color: var(--cg-midnight-700);
        .btn {
            border-color: var(--cg-midnight-400);
            color: var(--cg-midnight-400);
        }
    }

    .touch-opportunity {
        display: flex;
        flex-direction: column;
        align-items: center;
        img {
            margin-bottom: calc(var(--cg-space-1200) * -1);
        }
    }
    @media (min-width: 768px) {
        .touch-opportunity {
            margin-bottom: calc(var(--cg-space-800) * -1);
        }
    }
    @media (min-width: 960px) {
        .touch-opportunity {
            justify-content: space-between;
            flex-direction: row;
        }
    }

    .gift-messaging {
        display: flex;
        flex-direction: column;
        gap: var(--cg-space-1000);
        & > * {
            flex: 1;
        }
    }
    @media (min-width: 768px) {
        .gift-messaging {
            flex-direction: row;
            gap: var(--cg-space-300);
        }
    }
    [class*="-carousel"] {
        max-width: 250px;
        margin: calc(var(--cg-space-1100) * -1) auto 0;
        .splide__track {
            border-radius: var(--cg-border-radius-200);
            border: 5px solid var(--cg-celeste-600);
            overflow: hidden;
        }
        .splide__arrow {
            width: var(--cg-space-600);
            height: var(--cg-space-600);
            background-color: var(--cg-neutral-100);
            border: 4px solid var(--cg-celeste-600);
            opacity: 1 !important;
            &:hover {
                opacity: 1 !important;
            }
            svg {
                fill: var(--cg-celeste-600);
            }
        }
        .splide__arrow--prev {
            left: calc(var(--cg-space-300) * -1);
        }
        .splide__arrow--next {
            right: calc(var(--cg-space-300) * -1);
        }
    }
    .timing-tracking {
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
        & > * {
            flex: 1;
        }
    }
    @media (min-width: 768px) {
        .timing-tracking {
            flex-direction: row;
        }
    }

    .caring-for-customers-strategy {
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
        * > & {
            flex: 1;
        }
    }
    @media (min-width: 768px) {
        .caring-for-customers-strategy {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }

    .steps {
        background-color: var(--cg-midnight-100);
        display: flex;
        gap: var(--cg-space-300);
        flex-direction: column;
        & > * {
            flex: 1;
        }
        h3 {
            color: var(--cg-midnight-500);
        }
        .numbers-icon {
            background-color: var(--cg-neutral-100);
            color: var(--cg-midnight-500);
            border: 5px solid var(--cg-midnight-500);
            border-radius: 50%;
        }
    }
    @media (min-width: 960px) {
        .steps {
            flex-direction: row;
        }
    }
</style>

<MainLayout title="Client Giant">
    <Deck class="surface surface-midnight-dark">
        <!--Hero-->
        <Card surface="sunset" class="hero">
            <section>
                <Box class="copy">
                    <Stack justify="start">
                        <h1 data-font-size="600" data-font-weight="Bold">
                            Automated Gifting
                        </h1>
                        <p>Hands free gifting solutions built to scale.</p>
                        <Inline>
                            <Button label="Book a Demo" type="link" />
                            <Button
                                outline={true}
                                label="Get Started"
                                type="link"
                            />
                        </Inline>
                    </Stack>
                </Box>
                <Image
                    src={AutomatedGiftingHero}
                    alt=""
                    width="500"
                    height="500"
                />
            </section>
            <div class="hero-blob top-right-1"></div>
            <div class="hero-blob top-right-2"></div>
            <div class="hero-blob bottom-left-1"></div>
            <div class="hero-blob bottom-left-2"></div>
        </Card>

        <!--Take Care / Gifts -->
        <Card surface="white" stackOrder="2">
            <Stack size="700">
                <Box>
                    <section data-text-align="center">
                        <Stack>
                            <DotGrid center={true} color="sunset" />
                            <h2 data-font-size="600">
                                Take Care of Your Entire Contact List in Minutes
                            </h2>
                            <p class="max-900">
                                Client Giant enables you to automate care for
                                all your past clients in moments with a simple
                                import process. We take care of the burden of
                                decision making and data entry, talk to us about
                                how to get started today!
                            </p>
                        </Stack>
                    </section>
                </Box>
                <div class="take-care-tiles">
                    <Tile
                        surface="midnight-light"
                        paddingBlock="0"
                        paddingInline="0"
                        class="take-care-tile"
                    >
                        <section class="cg-stack">
                            <div class="duotone-icon">
                                <TopOfMind />
                            </div>
                            <Box paddingBlockStart="0">
                                <Stack
                                    data-text-align="center"
                                    data-stack-size="200"
                                >
                                    <h3 data-font-size="500">Top of Mind</h3>
                                    <p>As low as $11.99 /mo per recipient</p>
                                </Stack>
                            </Box>
                        </section>
                        <Box
                            surface="white"
                            borderRadius="200"
                            class="take-care-bordered-tile"
                        >
                            <Stack justify="center">
                                <ul
                                    class="cg-stack unlist"
                                    data-stack-size="200"
                                >
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        /> 4 Seasonal gifts sent quarterly
                                    </li>
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        /> 1 decision, we handle the rest
                                    </li>
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        />
                                        Digital engagements with every gift
                                    </li>
                                </ul>
                                <Button
                                    outline={true}
                                    type="link"
                                    label="Book a Demo"
                                />
                            </Stack>
                        </Box>
                    </Tile>
                    <Tile
                        surface="midnight-light"
                        paddingBlock="0"
                        paddingInline="0"
                        class="take-care-tile"
                    >
                        <section class="cg-stack">
                            <div class="duotone-icon">
                                <SpecialMoments />
                            </div>
                            <Box paddingBlockStart="0">
                                <Stack
                                    data-text-align="center"
                                    data-stack-size="200"
                                >
                                    <h3 data-font-size="500">
                                        Special Moments
                                    </h3>
                                    <p>As low as $39.99 /yr per recipient</p>
                                </Stack>
                            </Box>
                        </section>
                        <Box
                            surface="white"
                            borderRadius="200"
                            class="take-care-bordered-tile"
                        >
                            <Stack justify="center">
                                <ul
                                    class="cg-stack unlist"
                                    data-stack-size="200"
                                >
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        /> Never miss a birthday
                                    </li>
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        />
                                        Celebrate important anniversaries
                                    </li>
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        />Recognize employee milestones
                                    </li>
                                </ul>
                                <Button
                                    outline={true}
                                    type="link"
                                    label="Get Started"
                                />
                            </Stack>
                        </Box>
                    </Tile>
                    <Tile
                        surface="midnight-light"
                        paddingBlock="0"
                        paddingInline="0"
                        class="take-care-tile"
                    >
                        <section class="cg-stack">
                            <div class="duotone-icon">
                                <BulkGifting />
                            </div>
                            <Box paddingBlockStart="0">
                                <Stack
                                    data-text-align="center"
                                    data-stack-size="200"
                                >
                                    <h3 data-font-size="500">Bulk Gifting</h3>
                                    <p>Talk to us to learn more</p>
                                </Stack>
                            </Box>
                        </section>
                        <Box
                            surface="white"
                            borderRadius="200"
                            class="take-care-bordered-tile"
                        >
                            <Stack justify="center">
                                <ul
                                    class="cg-stack unlist"
                                    data-stack-size="200"
                                >
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        />
                                        All of your holiday gifting at once
                                    </li>
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        />Gifting for an annual event
                                    </li>
                                    <li class="cg-inline" data-align="center">
                                        <Icon
                                            size="300"
                                            fontSize="300"
                                            icon="fa-solid fa-circle-check"
                                        />Custom solutions
                                    </li>
                                </ul>
                                <Button
                                    outline={true}
                                    type="link"
                                    label="Talk to Sales"
                                />
                            </Stack>
                        </Box>
                    </Tile>
                </div>
            </Stack>
        </Card>

        <!--Touch Opportunity -->
        <Card surface="midnight-dark" stackOrder="1">
            <section class="touch-opportunity">
                <Box class="copy">
                    <Stack justify="start">
                        <Icon size="auto" icon="fa-light fa-qrcode" />
                        <h2 data-font-size="600">
                            Turn Every Touch Into an Opportunity
                        </h2>
                        <p>
                            With every gift that is sent, we include a unique QR
                            code based experience that allows you to connect
                            with your contacts
                        </p>
                        <ul>
                            <li>Link to your web and social properties</li>
                            <li>Engage with your CRM meeting links</li>
                            <li>
                                Allow recipients to say thank you and gain
                                feedback
                            </li>
                        </ul>
                    </Stack>
                </Box>
                <Box padding="0">
                    <Image src={iPhone} alt="" width="443" height="760" />
                </Box>
            </section>
        </Card>

        <!--Handle Everything, Caring Strategy, Step -->
        <Card surface="white">
            <Stack size="700" classes={["box-block-start-1000"]}>
                <section data-text-align="center">
                    <Stack>
                        <DotGrid center={true} color="sunset" />
                        <h2 data-font-size="600">We Handle Everything.</h2>
                        <p class="max-900">
                            The idea of taking care of everyone in your rolodex
                            can feel overwhelming, don't worry we got you
                            covered. Our automated gifting solutions are
                            designed to be completely hands-off so you can do
                            what you do best.
                        </p>
                    </Stack>
                </section>
                <Stack classes={["box-block-start-1000"]}>
                    <div class="gift-messaging">
                        <Tile surface="celeste-light">
                            <section>
                                <Stack justify="start">
                                    <div class="splide gift-carousel">
                                        <div class="splide__track">
                                            <ul class="splide__list">
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/XGYMza8Y.1724790579.png?ar=4:3&fit=crop&w=600"
                                                        alt="Occasionally Yours gift"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src=" https://clientgiant.imgix.net/SNTbIsmI.1750345651.png?ar=4:3&fit=crop&w=600"
                                                        alt="Heated Ice Cream Scoop gift"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/TtWmmsRt.1678827616.png?ar=4:3&fit=crop&w=600"
                                                        alt="The Question Game gift"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/cQp6E1Im.1709244186.png?ar=4:3&fit=crop&w=600"
                                                        alt="Out and About gift"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/3t4jOpPE.1668645027.png?ar=4:3&fit=crop&w=600"
                                                        alt="Platters and Boards gift"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <Icon
                                        size="auto"
                                        icon="fa-regular fa-gifts"
                                    />
                                    <h2 data-font-size="500">Gift Curation</h2>
                                    <p>
                                        Every touch that goes out is
                                        pre-selected and designed by our
                                        talented gifting team. We strive to
                                        deliver thoughtful experiences that are
                                        fitting for seasons, special moments or
                                        simply just because.
                                    </p>
                                </Stack>
                            </section>
                        </Tile>
                        <Tile surface="celeste-light">
                            <section>
                                <Stack justify="start">
                                    <div class="splide messaging-carousel">
                                        <div class="splide__track">
                                            <ul class="splide__list">
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/8Oe6zFc2.1685568725.png?ar=4:3&fit=crop&w=600"
                                                        alt="Summer Paradise Tea message"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/vSDD5kb3.1718297350.png?ar=4:3&fit=crop&w=600"
                                                        alt="Bylt Bag message"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/BFr4AzcR.1661211603.jpg?ar=4:3&fit=crop&w=600"
                                                        alt="The Question Game message"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/rOpvmdXZ.1707431569.png?ar=4:3&fit=crop&w=600"
                                                        alt="Out and About message"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                                <li class="splide__slide">
                                                    <img
                                                        src="https://clientgiant.imgix.net/be0N76gk.1668814885.jpg?ar=4:3&fit=crop&w=600"
                                                        alt="Platters and Boards message"
                                                        width="250"
                                                        height="187"
                                                    />
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <Icon
                                        size="auto"
                                        icon="fa-regular fa-envelope"
                                    />
                                    <h2 data-font-size="500">Messaging</h2>
                                    <p>
                                        Writing the right message isn't easy, we
                                        offer a pre-written message that goes
                                        out with every gift on a designed card
                                        (not a piece of paper). If you want to
                                        add an extra level of persoanl touch you
                                        may customize each message or use our AI
                                        message writing tool.
                                    </p>
                                </Stack>
                            </section>
                        </Tile>
                    </div>
                    <div class="timing-tracking">
                        <Tile surface="celeste-light">
                            <section>
                                <Stack justify="start">
                                    <Icon
                                        size="auto"
                                        icon="fa-regular fa-clock"
                                    />
                                    <h2 data-font-size="500">
                                        Timing and Control
                                    </h2>
                                    <p>
                                        All of our gifting solutions are
                                        designed to be delivered at the right
                                        time based on the recipient. Every
                                        situation is unique and may require fine
                                        tuning along the way, we give you full
                                        control on when items get delivered.
                                    </p>
                                </Stack>
                            </section>
                        </Tile>
                        <Tile surface="celeste-light">
                            <section>
                                <Stack justify="start">
                                    <Icon
                                        size="auto"
                                        icon="fa-regular fa-truck-fast"
                                    />
                                    <h2 data-font-size="500">
                                        Tracking Notifications
                                    </h2>
                                    <p>
                                        Stay on top of when your recipients will
                                        be receiving their items and when to
                                        expect responses. You will receive daily
                                        notification summaries of all items that
                                        have been recently delivered, full
                                        tracking details are available at all
                                        times inside of your account.
                                    </p>
                                </Stack>
                            </section>
                        </Tile>
                    </div>
                </Stack>
                <section class="caring-for-customers-strategy">
                    <Box>
                        <Stack justify="start" classes={["p-600"]}>
                            <h2 data-font-size="500">
                                Let Caring for Your Customers Be Your Marketing
                                Strategy
                            </h2>
                            <p>
                                While most companies stay hyper-focused on
                                marketing, the ones who are really winning are
                                pivoting their stance in ways that demonstrate
                                hospitality.
                            </p>

                            <Button
                                type="link"
                                label="Book a Meeting to Learn More"
                            />
                        </Stack>
                    </Box>
                    <Box paddingInlineEnd="0" paddingBlockEnd="0">
                        <NotVerySatisfied />
                    </Box>
                </section>
                <Tile class="steps">
                    <section>
                        <Stack>
                            <Icon
                                box={true}
                                class="icon numbers-icon"
                                fontSize="500"
                                icon="fa-regular fa-1"
                            />
                            <h3>Choose Recipients</h3>
                            <p class="p-400">
                                All we need are names and addresses. Import in
                                bulk or add more as you go.
                            </p>
                        </Stack>
                    </section>
                    <section>
                        <Stack>
                            <Icon
                                box={true}
                                class="icon numbers-icon"
                                fontSize="500"
                                icon="fa-regular fa-2"
                            />
                            <h3>Select Experience</h3>
                            <p class="p-400">
                                Choose from any of our services designed for
                                different industries and use cases.
                            </p>
                        </Stack>
                    </section>
                    <section>
                        <Stack>
                            <Icon
                                box={true}
                                class="icon numbers-icon"
                                fontSize="500"
                                icon="fa-regular fa-3"
                            />
                            <h3>Track and Monitor</h3>
                            <p class="p-400">
                                After your gifts are delivered, track your
                                progress on your referrals and engagements.
                            </p>
                        </Stack>
                    </section>
                </Tile>
            </Stack>
        </Card>
    </Deck>
</MainLayout>
