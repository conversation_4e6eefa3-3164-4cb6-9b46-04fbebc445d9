interface SubNavigationItem {
  label: string;
  description: string;
  id: string;
  href: string;
  icon: string;
  location: string;
}

interface NavigationItem {
  label: string;
  id: string;
  href?: string;
  subItems?: SubNavigationItem[];
}

export const navigationItems: NavigationItem[] = [
  {
    label: "What We Do",
    id: "what-we-do",
    subItems: [
      {
        label: "Automated Gifting",
        description: "Hands-free gifting in bulk",
        id: "automated-gifting",
        href: "/automated-gifting/",
        icon: "fa-regular fa-gifts",
        location: "automated-gifting",
      },
      {
        label: "Sales Acquisition",
        description: "Stand out with unique experiences",
        id: "sales-acquisition",
        href: "#",
        icon: "fa-regular fa-hand-holding-box",
        location: "sales-acquisition",
      },
      {
        label: "CRB",
        description: "Customer Relationship Builder",
        id: "customer-relationship-builder",
        href: "#",
        icon: "fa-regular fa-message-heart",
        location: "customer-relationship-builder",
      },
      {
        label: "Enterprise Solutions",
        description: "Integrations and Automation",
        id: "enterprise-solutions",
        href: "#",
        icon: "fa-regular fa-share-nodes",
        location: "enterprise-solutions",
      },
      {
        label: "Employee Happiness",
        description: "Retain and support employees",
        id: "employee-happiness",
        href: "#",
        icon: "fa-regular fa-user-tie",
        location: "employee-happiness",
      },
      {
        label: "On-demand Gifting",
        description: "Target specific moments",
        id: "on-demand-gifting",
        href: "#",
        icon: "fa-regular fa-ranking-star",
        location: "on-demand-gifting",
      },
    ],
  },
  {
    label: "Solutions",
    id: "solutions",
    subItems: [
      {
        label: "For Real Estate",
        description: "Real estate solutions",
        id: "for-real-estate",
        href: "#",
        icon: "fa-regular fa-house-user",
        location: "for-real-estate",
      },
      {
        label: "For Financial Advisors",
        description: "Financial advisor solutions",
        id: "for-financial-advisors",
        href: "#",
        icon: "fa-regular fa-money-bill-trend-up",
        location: "for-financial-advisors",
      },
      {
        label: "For Insurance Agents",
        description: "Insurance agent solutions",
        id: "for-insurance-agents",
        href: "#",
        icon: "fa-regular fa-house-chimney-crack",
        location: "for-insurance-agents",
      },
      {
        label: "Employee Happiness",
        description: "Retain and support employees",
        id: "employee-happiness",
        href: "#",
        icon: "fa-regular fa-user-tie",
        location: "employee-happiness",
      },
      {
        label: "Enterprise",
        description: "Integrations and Automation",
        id: "enterprise",
        href: "#",
        icon: "fa-regular fa-share-nodes",
        location: "enterprise",
      },
    ],
  },
  {
    label: "Why We Exist",
    id: "why-we-exist",
  },
  {
    label: "Resources",
    id: "resources",
  },
];
