---
export interface Props {
    class?: string;
    label: string;
    type: "submit" | "reset" | "button" | "link";
    frontIcon?: string;
    backIcon?: string;
    outline?: boolean;
}

const {
    label,
    class: className = "",
    type,
    frontIcon,
    backIcon,
    outline = false,
    ...rest
} = Astro.props;
---

{
    () => {
        if (type === "link") {
            return (
                <a
                    class="btn"
                    class:list={[{ ["-outline"]: outline }, className]}
                    {...rest}
                >
                    {() => {
                        if (!!frontIcon) return <i class={frontIcon} />;
                    }}
                    {label}
                    {() => {
                        if (!!backIcon) return <i class={backIcon} />;
                    }}
                </a>
            );
        } else {
            return (
                <button
                    class:list={[{ ["-outline"]: outline }, className]}
                    type={type}
                    {...rest}
                >
                    {() => {
                        if (!!frontIcon) return <i class={frontIcon} />;
                    }}
                    {label}
                    {() => {
                        if (!!backIcon) return <i class={backIcon} />;
                    }}
                </button>
            );
        }
    }
}
