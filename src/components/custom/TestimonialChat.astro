---
import { Image } from "astro:assets";

interface Props {
    image?: ImageMetadata;
    copy: string;
}

const { image, copy } = Astro.props;
---

<style>
    .image-circle {
        aspect-ratio: 1 / 1;
        border-radius: 100%;
        display: grid;
        place-items: center;
        padding: 5px;
        position: relative;
        grid-template-areas: "profile";
        max-width: 200px;
        background-color: var(--cg-sunset-100);
    }
    .image-circle img,
    .no-image {
        grid-area: profile;
        z-index: 1;
        width: calc(100% - 15px);
        height: auto;
        border-radius: inherit;
    }
    .no-image {
        background: var(--cg-midnight-200);
        height: calc(100% - 15px);
    }
    .chat-bubble {
        display: none;
    }
    @media (min-width: 480px) {
        .chat-bubble {
            display: block;
            border: 5px solid var(--cg-neutral-100);
            border-radius: var(--cg-border-radius-100);
            background-color: var(--cg-sunset-100);
            color: var(--cg-sunset-800);
            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
            position: absolute;
            top: 60%;
            right: -65%;
            width: 90%;
            transform: translate3d(0, -56%, 0);
            text-align: center;
            z-index: 5;
        }
    }
    [class*="chat-triangle-"] {
        position: absolute;
        width: 0;
        height: 0;
        border-style: solid;
        transform: rotate(45deg);
    }
    .chat-triangle-outer {
        border-width: 36.4px 21px 0px 21px;
        border-color: var(--cg-neutral-100) transparent transparent transparent;
        left: -22px;
        bottom: -20px;
        z-index: 6;
    }
    .chat-triangle-inner {
        border-width: 34.6px 20px 0px 20px;
        border-color: var(--cg-sunset-100) transparent transparent transparent;
        left: -16px;
        bottom: -14px;
        z-index: 7;
    }
</style>

<div class="testimonial-image">
    <div class="image-circle">
        {
            () => {
                if (image) {
                    return (
                        <Image src={image} alt="" width="400" height="400" />
                    );
                } else {
                    return <div class="no-image" />;
                }
            }
        }
        <div class="box-300 chat-bubble" set:html={copy}>
            <div class="chat-triangle-outer"></div>
            <div class="chat-triangle-inner"></div>
        </div>
    </div>
</div>
