---
import { Image } from "astro:assets";

import LaptopGuy from "../../assets/images/LaptopGuy.png";
import ReferralGirl from "../../assets/images/ReferralGirl.png";
import CommandCenter from "../../assets/images/command-center.svg";
---

<style>
    .command-center {
        position: relative;
    }
    .absolute-left,
    .absolute-right {
        display: none;
    }
    .center {
        width: 100%;
        margin-inline: auto;
        position: relative;
        z-index: 2;
    }
    .center-svg {
        width: 100%;
        height: auto;
    }
    @media (min-width: 768px) {
        .absolute-left {
            display: block;
            position: absolute;
            top: 40%;
            transform: translate3d(0, -40%, 0);
            left: 0;
            width: 25%;
            height: auto;
        }
        .absolute-right {
            display: block;
            position: absolute;
            top: 40%;
            transform: translate3d(0, -40%, 0);
            right: 0;
            width: 25%;
            height: auto;
        }
        .center {
            width: 60%;
        }
    }
</style>

<div class="command-center">
    <Image
        class="absolute-left"
        src={ReferralGirl}
        alt="Girl in yellow sweater on her teal phone"
        width="640"
        height="774"
    />
    <div class="center">
        <CommandCenter class="center-svg" />
    </div>
    <Image
        class="absolute-right"
        src={LaptopGuy}
        alt="Guy in grey-teal t-shirt on his laptop"
        width="640"
        height="774"
    />
</div>
