---
import C<PERSON><PERSON><PERSON><PERSON>ogo from "../assets/client-giant.svg";
import Inline from "./Inline.astro";
import Stack from "./Stack.astro";
---

<style>
    .surface-footer {
        padding: var(--cg-space-600);

        --cg-surface-background: var(--cg-midnight-100);
        --cg-surface-foreground: var(--cg-midnight-800);

        --cg-surface-button-solid-foreground: var(--cg-midnight-100);
        --cg-surface-button-solid-background: var(--cg-midnight-800);
        --cg-surface-button-solid-foreground-hover: var(--cg-midnight-100);
        --cg-surface-button-solid-background-hover: var(--cg-midnight-700);

        --cg-surface-button-outline-foreground: var(--cg-midnight-800);
        --cg-surface-button-outline-foreground-hover: var(--cg-midnight-700);

        --cg-surface-icon-foreground: var(--cg-midnight-800);
    }
    @media (min-width: 768px) {
        .surface-footer {
            padding-inline: var(--cg-space-1000);
        }
    }

    .secondary-nav,
    .nav-sections {
        display: flex;
        gap: var(--cg-space-600);
        flex-direction: column;
        a {
            text-decoration: none;
        }
    }
    @media (min-width: 480px) {
        .nav-sections {
            flex-direction: row;
        }
        .nav-section + .nav-section {
            border-left: 1px solid var(--cg-midnight-200);
            padding-left: var(--cg-space-300);
        }
    }
    @media (min-width: 768px) {
        .nav-section + .nav-section {
            padding-left: var(--cg-space-600);
        }
    }
    @media (min-width: 960px) {
        .secondary-nav {
            flex-direction: row;
        }
        .nav-section:first-child {
            border-left: 1px solid var(--cg-midnight-200);
            padding-left: var(--cg-space-600);
        }
    }
    .link {
        text-align: left;
        text-decoration: underline;
        font-weight: var(--cg-font-weight-300);
    }
</style>

<footer class="surface surface-footer">
    <Stack>
        <nav class="secondary-nav" aria-label="Secondary">
            <div>
                <Stack>
                    <a href="/">
                        <ClientGiantLogo />
                    </a>
                    <Inline size="200">
                        Follow Us:
                        <a href="https://www.facebook.com/theclientgiant/"
                            ><i class="fa-brands fa-facebook"></i></a
                        >
                        <a
                            href="https://www.instagram.com/theclientgiant/?hl=en"
                            ><i class="fa-brands fa-instagram"></i></a
                        >
                        <a href="https://www.linkedin.com/company/clientgiant"
                            ><i class="fa-brands fa-linkedin"></i></a
                        >
                    </Inline>
                    <Inline size="200">
                        <a class="btn" href="#">Book a Demo</a>
                        <a class="btn" href="#">Talk to Sales</a>
                    </Inline>
                </Stack>
            </div>
            <div class="nav-sections">
                <section class="nav-section">
                    <Stack>
                        <h2>What We Do</h2>
                        <ul class="unlist cg-stack" data-stack-size="200">
                            <li><a href="#">Automated Gifting</a></li>
                            <li><a href="#">Ratings Booster</a></li>
                            <li><a href="#">Relationship Builder</a></li>
                            <li><a href="#">Sequences</a></li>
                            <li><a href="#">Onboarding</a></li>
                            <li><a href="#">Engagements</a></li>
                        </ul>
                    </Stack>
                </section>
                <section class="nav-section">
                    <Stack>
                        <h2>Solutions</h2>
                        <ul class="unlist cg-stack" data-stack-size="200">
                            <li><a href="#">For Real Estate</a></li>
                            <li><a href="#">For Financial Advisors</a></li>
                            <li><a href="#">For Insurance Agents</a></li>
                            <li><a href="#">Employee Happiness</a></li>
                            <li><a href="#">Enterprise</a></li>
                        </ul>
                    </Stack>
                </section>
                <section class="nav-section">
                    <Stack>
                        <h2>Resources</h2>
                        <ul class="unlist cg-stack" data-stack-size="200">
                            <li><a href="#">Blog</a></li>
                            <li><a href="#">Knowledgebase</a></li>
                            <li><a href="#">Become a Vendor</a></li>
                            <li><a href="#">Careers</a></li>
                            <li><a href="#">Case Studies</a></li>
                        </ul>
                    </Stack>
                </section>
            </div>
        </nav>
        <nav aria-label="Legal">
            <div>&copy; Client Giant Inc</div>
            <Inline size="200">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <button class="link -outline" type="button"
                    >Manage Cookie Preferences</button
                >
            </Inline>
        </nav>
    </Stack>
</footer>
