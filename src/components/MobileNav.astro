---
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "../assets/client-giant.svg";
import Button from "./Button.astro";
import Icon from "./Icon.astro";
import Stack from "./Stack.astro";

const inFocus = false;
---

<script>
  const close = document.querySelector(".close-btn");
  const body = document.querySelector("body");
  const level1Links = document.querySelectorAll(".first-panel button");

  level1Links.forEach((link) => {
    link.addEventListener("click", (e) => {
      const target = e.target.getAttribute("data-level-2");
      console.log("linked linked");
    });
  });

  if (close && body) {
    close.addEventListener("click", () => {
      body.classList.remove("mobile-menu-open");
    });
  }
</script>

<style is:global>
  body.mobile-menu-open {
    overflow: hidden;

    .mobile-nav {
      display: block;
    }
  }

  .mobile-nav {
    display: none;
  }
</style>

<style>
  .mobile-nav {
    position: fixed;
    top: var(--cg-space-500);
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }
  .cg-inline {
    position: relative;
    gap: var(--cg-space-300);
    justify-content: space-between;
    border-bottom: 1px solid var(--cg-midnight-200);
    padding: var(--cg-space-300) var(--cg-space-600);
  }

  .panels {
    display: grid;
    grid-template-columns: 1fr;
  }

  .panels__panel {
    grid-area: 1 / 1 / 1 / 1;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;

    & > * {
      z-index: 1;
      pointer-events: all;
      background-color: var(--cg-neutral-100);
      transition: all 0.35s ease-out;
      transform: translateX(100vw);
    }

    &.in {
      & > * {
        transform: translateX(0);
      }
    }

    &.focus {
      height: auto;
      overflow: visible;
    }
  }

  .second-panel {
    padding: var(--cg-space-300) var(--cg-space-600);
    transition: all 0.35s ease-out;
    transform: translateX(100vw);
    li {
      display: flex;
      gap: var(--cg-space-200);
      align-items: center;
      position: relative;
    }
    li a {
      white-space: nowrap;
      text-decoration: none;
      font-weight: var(--cg-font-weight-600);
    }

    .-subheading {
      font-size: var(--cg-font-size-100);
      color: var(--cg-midnight-400);
      white-space: nowrap;
    }
  }
  .link {
    color: var(--cg-midnight-800);
  }
</style>

<nav class="surface mobile-nav">
  <div class="cg-inline">
    <ClientGiantLogo />
    <Button
      class="link -outline"
      label=""
      type="button"
      frontIcon="fa-solid fa-x"
      class="close-btn"
    />
  </div>
  <div class="panels">
    <div class:list={["panels__panel in", { ["focus"]: !inFocus }]}>
      <ul class="unlist first-panel">
        <li class="cg-inline">
          <Button
            class="link -outline stretched-link"
            label="What We Do"
            type="button"
            data-level-2="what-we-do"
          />
          <div>
            <i class="fa-solid fa-chevron-right"></i>
          </div>
        </li>
        <li class="cg-inline">
          <Button
            class="link -outline stretched-link"
            label="Solutions"
            type="button"
            data-level-2="solutions"
          />
          <div>
            <i class="fa-solid fa-chevron-right"></i>
          </div>
        </li>
        <li class="cg-inline">
          <Button
            class="link -outline stretched-link"
            label="Why We Exist"
            type="button"
            data-level-2="why-we-exist"
          />
          <div>
            <i class="fa-solid fa-chevron-right"></i>
          </div>
        </li>
        <li class="cg-inline">
          <Button
            class="link -outline stretched-link"
            label="Resources"
            type="button"
            data-level-2="resources"
          />
          <div>
            <i class="fa-solid fa-chevron-right"></i>
          </div>
        </li>
      </ul>
    </div>

    <div class:list={["panels__panel", { ["in focus"]: inFocus }]}>
      <Stack classes={["second-panel"]}>
        <h3>Why We Exist</h3>
        <ul class="unlist cg-stack" data-stack-size="300">
          <li>
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-gifts"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Automated Gifting</a>
              <div class="-subheading">Hands-free gifting in bulk</div>
            </Stack>
          </li>
          <li>
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-hand-holding-box"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Sales Acquisition</a>
              <div class="-subheading">Stand out with unique experiences</div>
            </Stack>
          </li>
          <li>
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-message-heart"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">CRB</a>
              <div class="-subheading">Customer Relationship Builder</div>
            </Stack>
          </li>
          <li>
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-share-nodes"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Enterprise Solutions</a>
              <div class="-subheading">Integrations and Automation</div>
            </Stack>
          </li>
          <li>
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-user-tie"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Employee Happiness</a>
              <div class="-subheading">Retain and support employees</div>
            </Stack>
          </li>
          <li>
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-ranking-star"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">On-demand Gifting</a>
              <div class="-subheading">Target specific moments</div>
            </Stack>
          </li>
        </ul>
      </Stack>
    </div>
  </div>
</nav>
