---
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "../assets/client-giant.svg";
import Button from "./Button.astro";
import Icon from "./Icon.astro";
import Stack from "./Stack.astro";

import { navigationItems } from "../data/navigation";

const subNavigationItems = navigationItems.filter((item) => item.subItems);
---

<script>
  // Mobile navigation controller
  class MobileNavController {
    private closeBtn: HTMLElement | null;
    private body: HTMLElement | null;
    private level1Links: NodeListOf<Element>;
    private mobileNav: HTMLElement | null;
    private subnavs: NodeListOf<Element>;
    private backBtns: NodeListOf<Element>;

    constructor() {
      this.closeBtn = document.querySelector(".close-btn");
      this.body = document.querySelector("body");
      this.level1Links = document.querySelectorAll(".first-panel button");
      this.mobileNav = document.querySelector(".mobile-nav");
      this.subnavs = document.querySelectorAll(".subnav");
      this.backBtns = document.querySelectorAll(".back-btn");

      this.init();
    }

    private init(): void {
      this.setupCloseButton();
      this.setupBackButtons();
      this.setupNavigationLinks();
    }

    private setupCloseButton(): void {
      if (this.closeBtn && this.body) {
        this.closeBtn.addEventListener("click", () => {
          this.body?.classList.remove("mobile-menu-open");
        });
      }
    }

    private setupBackButtons(): void {
      this.backBtns.forEach((btn) => {
        btn.addEventListener("click", () => {
          this.hideAllSubnavs();
          this.mobileNav?.classList.remove("focus");
        });
      });
    }

    private setupNavigationLinks(): void {
      this.level1Links.forEach((link) => {
        link.addEventListener("click", (e) => {
          const target = this.getTargetFromEvent(e);
          if (target) {
            this.showSubnav(target);
          }
        });
      });
    }

    private getTargetFromEvent(e: Event): string | undefined {
      return e.target instanceof HTMLElement
        ? e.target.dataset["level-2"]
        : undefined;
    }

    private hideAllSubnavs(): void {
      this.subnavs.forEach((subnav) => {
        subnav.classList.remove("in");
      });
    }

    private showSubnav(targetId: string): void {
      this.hideAllSubnavs();
      document.getElementById(targetId)?.classList.add("in");
    }
  }

  // Initialize when DOM is ready
  document.addEventListener("DOMContentLoaded", () => {
    new MobileNavController();
  });
</script>

<style is:global>
  /* Global mobile navigation styles */
  body.mobile-menu-open {
    overflow: hidden;

    .mobile-nav {
      display: block;
    }
  }

  .mobile-nav {
    display: none;
  }
</style>

<style>
  /* Mobile navigation container */
  .mobile-nav {
    position: fixed;
    top: var(--cg-space-500);
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }

  /* Header section with logo and close button */
  .cg-inline {
    position: relative;
    gap: var(--cg-space-300);
    justify-content: space-between;
    border-bottom: 1px solid var(--cg-midnight-200);
    padding: var(--cg-space-300) var(--cg-space-600);
  }

  /* Panel system for navigation layers */
  .panels {
    display: grid;
    grid-template-columns: 1fr;
  }

  .panels__panel {
    grid-area: 1 / 1 / 1 / 1;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;

    & > * {
      z-index: 1;
      pointer-events: all;
      background-color: var(--cg-neutral-100);
      transition: all 0.35s ease-out;
      transform: translateX(100vw);
    }

    &.in > * {
      transform: translateX(0);
    }

    &.focus {
      height: auto;
      overflow: visible;
    }
  }

  /* Secondary panel styles */
  .second-panel {
    padding: var(--cg-space-300) var(--cg-space-600);
    transition: all 0.35s ease-out;
    transform: translateX(100vw);

    li {
      display: flex;
      gap: var(--cg-space-200);
      align-items: center;
      position: relative;
    }

    li a {
      white-space: nowrap;
      text-decoration: none;
      font-weight: var(--cg-font-weight-600);
    }

    .-subheading {
      font-size: var(--cg-font-size-100);
      color: var(--cg-midnight-400);
      white-space: nowrap;
    }
  }

  /* Link styles */
  .link {
    color: var(--cg-midnight-800);
  }
</style>

<nav class="surface mobile-nav">
  <!-- Header with logo and close button -->
  <div class="cg-inline">
    <ClientGiantLogo />
    <Button
      class="link -outline close-btn"
      label=""
      type="button"
      frontIcon="fa-solid fa-x"
    />
  </div>

  <!-- Navigation panels -->
  <div class="panels">
    <!-- Main navigation panel -->
    <div class="panels__panel in">
      <ul class="unlist first-panel">
        {
          navigationItems.map((item) => (
            <li class="cg-inline">
              <Button
                class="link -outline stretched-link"
                label={item.label}
                type="button"
                data-level-2={item.id}
              />
              <div>
                <i class="fa-solid fa-chevron-right" />
              </div>
            </li>
          ))
        }
      </ul>
    </div>

    <!-- What We Do submenu -->
    {
      subNavigationItems.map((item) => (
        <div id={item.id} class="subnav panels__panel">
          <Stack classes={["second-panel"]}>
            <Button type="button" label="Back" class="back-btn" />
            <h3>What We Do</h3>
            <ul class="unlist cg-stack" data-stack-size="300">
              {item.subItems.map((subitem) => (
                <li>
                  <Icon
                    box={true}
                    class="icon"
                    size="600"
                    fontSize="300"
                    borderRadius="100"
                    icon={subitem.icon}
                  />
                  <Stack size="100">
                    <a href={subitem.href} class="stretched-link">
                      {subitem.label}
                    </a>
                    <div class="-subheading">{subitem.description}</div>
                  </Stack>
                </li>
              ))}
            </ul>
          </Stack>
        </div>
      ))
    }
  </div>
</nav>
