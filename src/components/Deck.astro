---
interface Props {
    class: string;
}

const { class: className } = Astro.props;
---

<div class="cg-deck" class:list={className}><slot /></div>

<style is:global>
    .cg-deck > * + * {
        margin-top: calc(var(--card-border-radius) * -1);
    }
    @media (min-width: 768px) {
        .cg-deck > * + * {
            margin-top: calc(var(--card-border-radius) * -2);
        }
    }

    .cg-deck .card:last-child {
        border-bottom-left-radius: var(--cg-border-radius-0);
        border-bottom-right-radius: var(--cg-border-radius-0);
    }

    @keyframes card-journey {
        0% {
            transform: translateY(0);
        }
        25% {
            transform: translateY(calc(var(--card-border-radius) * -1));
        }
        75% {
            transform: translateY(calc(var(--card-border-radius) * -1));
        }
        100% {
            transform: translateY(0);
        }
    }

    .cg-deck > * + * {
        animation: card-journey linear both;
        animation-timeline: view();
        animation-range: entry 0% exit 100%;
    }
</style>
