---
// A tile is a collection of content with a rounded border.

import type { BOX_PROPS } from "../types/box-props.type";
interface Props extends Omit<BOX_PROPS, "borderRadius" | "padding"> {}

import Box from "./Box.astro";

const {
    surface = "white",
    paddingInline = null,
    paddingBlock = null,
    paddingBlockStart = null,
    paddingBlockEnd = null,
    paddingInlineStart = null,
    paddingInlineEnd = null,
    ...rest
} = Astro.props;
---

<Box
    surface={surface}
    borderRadius="300"
    paddingInline={paddingInline}
    paddingBlock={paddingBlock}
    paddingBlockStart={paddingBlockStart}
    paddingBlockEnd={paddingBlockEnd}
    paddingInlineStart={paddingInlineStart}
    paddingInlineEnd={paddingInlineEnd}
    {...rest}
>
    <slot />
</Box>

<style is:global>
    .tile {
        --tile-border-radius: var(--cg-border-radius-300);
        border-radius: var(--tile-border-radius);
    }
</style>
