---
import type { SPACE_SIZES } from "../types/spaces.type";

export interface Props {
    size?: SPACE_SIZES | "auto";
    fontSize?: SPACE_SIZES;
    borderRadius?: SPACE_SIZES;
    icon: string;
    box?: boolean;
    class?: string;
}
const {
    size = "900",
    fontSize = "600",
    borderRadius = "200",
    icon,
    class: className,
    box = false,
    ...rest
} = Astro.props;
---

<style is:global>
    [class*="icon-size-"] {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
    .icon-size-auto {
        width: auto;
        height: auto;
    }
    .icon-size-300 {
        width: var(--cg-space-300);
        height: var(--cg-space-300);
    }
    .icon-size-400 {
        width: var(--cg-space-400);
        height: var(--cg-space-400);
    }
    .icon-size-500 {
        width: var(--cg-space-500);
        height: var(--cg-space-500);
    }
    .icon-size-600 {
        width: var(--cg-space-600);
        height: var(--cg-space-600);
    }
    .icon-size-700 {
        width: var(--cg-space-700);
        height: var(--cg-space-700);
    }
    .icon-size-800 {
        width: var(--cg-space-800);
        height: var(--cg-space-800);
    }
    .icon-size-900 {
        width: var(--cg-space-900);
        height: var(--cg-space-900);
    }
    .icon-size-1000 {
        width: var(--cg-space-1000);
        height: var(--cg-space-1000);
    }
    .icon-size-1100 {
        width: var(--cg-space-1100);
        height: var(--cg-space-1100);
    }

    .font-size-300 {
        font-size: var(--cg-font-size-300);
    }
    .font-size-400 {
        font-size: var(--cg-font-size-400);
    }
    .font-size-500 {
        font-size: var(--cg-font-size-500);
    }
    .font-size-600 {
        font-size: var(--cg-font-size-600);
    }
    /* There aren't font sizes this large */
    .font-size-700 {
        font-size: var(--cg-space-700);
    }
    .font-size-800 {
        font-size: var(--cg-space-800);
    }
</style>

<div
    class:list={[
        className,
        { ["icon"]: !box },
        { ["icon-box"]: !!box },
        `icon-size-${size}`,
        `border-radius-${borderRadius}`,
    ]}
    {...rest}
>
    <i class:list={[icon, `font-size-${fontSize}`]}></i>
</div>
