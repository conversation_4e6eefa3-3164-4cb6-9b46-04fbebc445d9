---
interface Props {
  video: string;
}
const { video } = Astro.props;
---

<style>
  .video-container {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--cg-space-400);
    border-radius: var(--cg-border-radius-200);
    background-color: var(--cg-midnight-1000);
  }

  .video-wrapper {
    width: 100%;
    padding: var(--cg-space-300);
    border-radius: var(--cg-border-radius-200);
    background-color: var(--cg-midnight-200);
  }

  .video {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    border-radius: var(--cg-border-radius-200);
    background-color: white;
  }

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    box-shadow: none;
    border-radius: var(--cg-border-radius-200);
  }
</style>

<div class="video-container">
  <div class="video-wrapper">
    <div class="video">
      <iframe
        src={video}
        allow="autoplay; fullscreen"
        allowfullscreen
        :title="title"
        loading="lazy"
        data-vimeo-defer></iframe>
    </div>
  </div>
</div>
