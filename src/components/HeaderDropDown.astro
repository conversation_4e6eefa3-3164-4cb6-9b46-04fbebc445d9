---
interface Props {
  summary: string;
}

const { summary } = Astro.props;
---

<style>
  .dropdown {
    position: relative;
    z-index: 1000;
  }
  summary {
    cursor: pointer;
    display: block;
    color: var(--cg-neutral-100);
    white-space: nowrap;
  }
  summary::-webkit-details-marker {
    display: none;
  }
  .content {
    position: absolute;
    margin-top: 1em;
    left: 50%;
    opacity: 0;
    transform: translate3d(-50%, 0, 0);
    pointer-events: none;
    transition: opacity 0.35s ease-in-out;
    padding: var(--cg-space-300);
    box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.4);
  }

  details[open] + .content,
  .dropdown:hover .content {
    opacity: 1;
    pointer-events: auto;
    transition: opacity 0.35s ease-in-out;
  }
  .content::after {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    top: -1em;
    left: 50%;
    transform: translate3d(-50%, 0, 0);
    border-bottom: 24px solid var(--cg-neutral-100);
    border-left: 32px solid transparent;
    border-right: 32px solid transparent;
  }
</style>
<div class="dropdown">
  <details>
    <summary>{summary}</summary>
  </details>
  <div class="content surface border-radius-100">
    <slot />
  </div>
</div>
