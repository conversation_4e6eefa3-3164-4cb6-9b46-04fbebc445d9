---
interface Props {
    color: string;
    size?: number;
    rows?: number;
    cols?: number;
    center?: boolean;
}

const { center = false, color, ...rest } = Astro.props;
---

<style is:global>
    .dot-grid {
        --cg-dot-grid-size: 6px;
        --cg-dot-grid-color: var(--cg-sunset-600);
        --cg-dot-grid-rows: 3;
        --cg-dot-grid-cols: 2;

        background-image: radial-gradient(
            var(--cg-dot-grid-color) calc(var(--cg-dot-grid-size) * 0.5),
            transparent calc(var(--cg-dot-grid-size) * 0.5)
        );
        background-color: transparent;
        background-position: calc(
            -1 * var(--cg-dot-grid-size) -1 * var(--cg-dot-grid-size)
        );

        background-size: calc(var(--cg-dot-grid-size) * 3)
            calc((var(--cg-dot-grid-size) * 3));
        width: calc(var(--cg-dot-grid-rows) * var(--cg-dot-grid-size) * 3);
        height: calc(var(--cg-dot-grid-cols) * var(--cg-dot-grid-size) * 3);
    }
    .dot-grid.-center {
        background-position: 0 0;
        margin-inline: auto;
    }
    .dot-grid.sunset {
        --cg-dot-grid-color: var(--cg-sunset-600);
    }
    .dot-grid.celeste {
        --cg-dot-grid-color: var(--cg-celeste-300);
    }
    .dot-grid.midnight {
        --cg-dot-grid-color: var(--cg-midnight-400);
    }
</style>

<div class:list={["dot-grid", [{ "-center": center }], color]}></div>
