---
import Inline from "./Inline.astro";
import Box from "./Box.astro";
---

<style>
    .call-sales {
        background-color: var(--cg-midnight-1000);
        color: var(--cg-neutral-100);
        display: flex;
        font-size: var(--cg-font-size-100);
        justify-content: flex-end;
        position: sticky;
        top: 0;
        z-index: 100;
    }
    .call-sales a {
        color: var(--cg-neutral-100);
    }
    .call-sales a.phone {
        text-decoration: none;
    }
</style>

<Box class="call-sales" paddingBlock={"200"}>
    <Inline size="200">
        <a href="tel:************" class="phone">
            <i class="fa-regular fa-phone-volume"></i> Contact Sales (*************
        </a>
        <span> &bull; </span>
        <a class="demo" href="#">Book Demo</a>
    </Inline>
</Box>
