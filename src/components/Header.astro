---
import C<PERSON><PERSON><PERSON><PERSON>ogo from "../assets/client-giant-white.svg";
import HeaderDropDown from "../components/HeaderDropDown.astro";
import Button from "./Button.astro";
import Icon from "./Icon.astro";
import Inline from "./Inline.astro";
import Stack from "./Stack.astro";
---

<script>
  const hamburger = document.querySelector(".mobile-menu-toggle");
  const body = document.querySelector("body");

  if (hamburger && body) {
    hamburger.addEventListener("click", () => {
      console.log("click");
      body.classList.add("mobile-menu-open");
    });
  }
</script>

<style>
  header {
    background-color: var(--cg-midnight-1000);
    container: header / inline-size;
    z-index: 50;
  }

  .primary-nav {
    display: flex;
    gap: var(--cg-space-600);
    justify-content: flex-start;
    align-items: center;
    background-color: var(--cg-midnight-900);

    padding: var(--cg-space-200) var(--cg-space-600);
  }

  .nav-links {
    display: none;
  }

  .cta-group {
    display: none;
  }

  @container header (width > 1080px) {
    .nav-links {
      display: flex;
      gap: var(--cg-space-300);
    }

    .cta-group {
      display: flex;
      gap: var(--cg-space-300);
    }

    .mobile-menu-toggle {
      display: none;
    }
  }

  ul.grid {
    display: grid;
    gap: var(--cg-space-200);
    grid-template-columns: repeat(2, 1fr);
  }
  li {
    display: flex;
    gap: var(--cg-space-200);
    align-items: center;
    position: relative;
  }
  li a {
    white-space: nowrap;
    text-decoration: none;
    font-weight: var(--cg-font-weight-600);
  }
  .-subheading {
    font-size: var(--cg-font-size-100);
    color: var(--cg-midnight-400);
    white-space: nowrap;
  }

  .cta-group,
  .mobile-menu-toggle {
    margin-inline-start: auto;
  }
  .cta-group .btn {
    min-width: 12ch;
    background-color: var(--cg-midnight-500);
    border-color: var(--cg-midnight-500);
    color: var(--cg-neutral-100);
    &:hover {
      background-color: var(--cg-midnight-600);
      border-color: var(--cg-midnight-600);
    }
  }
</style>

<header>
  <nav class="primary-nav">
    <a href="/">
      <ClientGiantLogo />
    </a>

    <Inline classes={["nav-links"]}>
      <HeaderDropDown summary="What We Do">
        <ul class="grid unlist">
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-gifts"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Automated Gifting</a>
              <div class="-subheading">Hands-free gifting in bulk</div>
            </Stack>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-hand-holding-box"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Sales Acquisition</a>
              <div class="-subheading">Stand out with unique experiences</div>
            </Stack>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-message-heart"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">CRB</a>
              <div class="-subheading">Customer Relationship Builder</div>
            </Stack>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-share-nodes"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Enterprise Solutions</a>
              <div class="-subheading">Integrations and Automation</div>
            </Stack>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-user-tie"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">Employee Happiness</a>
              <div class="-subheading">Retain and support employees</div>
            </Stack>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-ranking-star"
            />
            <Stack size="100">
              <a href="#" class="stretched-link">On-demand Gifting</a>
              <div class="-subheading">Target specific moments</div>
            </Stack>
          </li>
        </ul>
      </HeaderDropDown>

      <HeaderDropDown summary="Solutions">
        <ul class="grid unlist">
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-house-user"
            />
            <a href="#" class="stretched-link">For Real Estate</a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-money-bill-trend-up"
            />
            <a href="#" class="stretched-link">For Financial Advisors</a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-house-chimney-crack"
            />
            <a href="#" class="stretched-link">For Insurance Agents</a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-user-tie"
            />
            <a href="#" class="stretched-link">Employee Happiness</a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-starship"
            />
            <a href="#" class="stretched-link">Enterprise</a>
          </li>
        </ul>
      </HeaderDropDown>

      <HeaderDropDown summary="Why We Exist">
        <ul class="grid unlist">
          <li class="box-200">
            <a href="#" class="stretched-link">Link 1</a>
          </li>
          <li class="box-200">
            <a href="#" class="stretched-link">Link 2</a>
          </li>
          <li class="box-200">
            <a href="#" class="stretched-link">Link 3</a>
          </li>
          <li class="box-200">
            <a href="#" class="stretched-link">Link 4</a>
          </li>
        </ul></HeaderDropDown
      >
      <HeaderDropDown summary="Resources">
        <ul class="grid unlist">
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-square-rss"
            />
            <a href="http://blog.clientgiant.com" class="stretched-link">Blog</a
            >
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-user-graduate"
            />
            <a href="https://4906993.hs-sites.com/" class="stretched-link">
              Knowledgebase
            </a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-shop"
            />
            <a href="#" class="stretched-link">Become a Vendor</a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-building-magnifying-glass"
            />
            <a href="#" class="stretched-link">Careers</a>
          </li>
          <li class="box-200">
            <Icon
              box={true}
              class="icon"
              size="600"
              fontSize="300"
              borderRadius="100"
              icon="fa-regular fa-file-magnifying-glass"
            />
            <a href="#" class="stretched-link">Case Studies</a>
          </li>
        </ul>
      </HeaderDropDown>
    </Inline>

    <Inline classes={["cta-group"]}>
      <Button type="link" label="Login" />
      <Button type="link" label="Get Started" />
    </Inline>

    <Button class="mobile-menu-toggle" type="button" label="🍔" />
  </nav>
</header>
