---
import Client<PERSON><PERSON><PERSON>ogo from "../assets/client-giant-white.svg";
import HeaderDropDown from "../components/HeaderDropDown.astro";
import Button from "./Button.astro";
import Icon from "./Icon.astro";
import Inline from "./Inline.astro";
import Stack from "./Stack.astro";

import { navigationItems } from "../data/navigation";
---

<script>
  const hamburger = document.querySelector(".mobile-menu-toggle");
  const body = document.querySelector("body");

  if (hamburger && body) {
    hamburger.addEventListener("click", () => {
      console.log("click");
      body.classList.add("mobile-menu-open");
    });
  }
</script>

<style>
  header {
    background-color: var(--cg-midnight-1000);
    container: header / inline-size;
    z-index: 50;
  }

  .primary-nav {
    display: flex;
    gap: var(--cg-space-600);
    justify-content: flex-start;
    align-items: center;
    background-color: var(--cg-midnight-900);

    padding: var(--cg-space-200) var(--cg-space-600);
  }

  .nav-links {
    display: none;
  }

  .cta-group {
    display: none;
  }

  @container header (width > 1080px) {
    .nav-links {
      display: flex;
      gap: var(--cg-space-300);
    }

    .cta-group {
      display: flex;
      gap: var(--cg-space-300);
    }

    .mobile-menu-toggle {
      display: none;
    }
  }

  ul.grid {
    display: grid;
    gap: var(--cg-space-200);
    grid-template-columns: repeat(2, 1fr);
  }
  li {
    display: flex;
    gap: var(--cg-space-200);
    align-items: center;
    position: relative;
  }
  li a {
    white-space: nowrap;
    text-decoration: none;
    font-weight: var(--cg-font-weight-600);
  }
  .-subheading {
    font-size: var(--cg-font-size-100);
    color: var(--cg-midnight-400);
    white-space: nowrap;
  }

  .cta-group,
  .mobile-menu-toggle {
    margin-inline-start: auto;
  }
  .cta-group .btn {
    min-width: 12ch;
    background-color: var(--cg-midnight-500);
    border-color: var(--cg-midnight-500);
    color: var(--cg-neutral-100);
    &:hover {
      background-color: var(--cg-midnight-600);
      border-color: var(--cg-midnight-600);
    }
  }
</style>

<header>
  <nav class="primary-nav">
    <a href="/">
      <ClientGiantLogo />
    </a>

    <Inline classes={["nav-links"]}>
      {
        navigationItems.map((item) => {
          if (item.subItems) {
            return (
              <HeaderDropDown summary={item.label}>
                <ul class="grid unlist">
                  {item.subItems.map((subItem) => (
                    <li class="box-200">
                      <Icon
                        box={true}
                        class="icon"
                        size="600"
                        fontSize="300"
                        borderRadius="100"
                        icon={subItem.icon}
                      />
                      <Stack size="100">
                        <a href={subItem.href} class="stretched-link">
                          {subItem.label}
                        </a>
                        <div class="-subheading">{subItem.description}</div>
                      </Stack>
                    </li>
                  ))}
                </ul>
              </HeaderDropDown>
            );
          } else {
            return (
              <a href={item.location} class="stretched-link">
                {item.label}
              </a>
            );
          }
        })
      }
    </Inline>

    <Inline classes={["cta-group"]}>
      <Button type="link" label="Login" />
      <Button type="link" label="Get Started" />
    </Inline>

    <Button class="mobile-menu-toggle" type="button" label="🍔" />
  </nav>
</header>
