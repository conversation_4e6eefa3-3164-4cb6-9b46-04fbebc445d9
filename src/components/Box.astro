---
import type { BOX_PROPS } from "../types/box-props.type";

interface Props extends BOX_PROPS {}

const {
    surface = null,
    borderRadius = "0",
    padding = "600",
    paddingInline = null,
    paddingBlock = null,
    paddingBlockStart = null,
    paddingBlockEnd = null,
    paddingInlineStart = null,
    paddingInlineEnd = null,
    class: className = "",
    ...rest
} = Astro.props;
---

<div
    class:list={[
        className,
        { surface: !!surface },
        { [`surface-${surface}`]: !!surface },
        `border-radius-${borderRadius}`,
        `box-${padding}`,
        { [`box-inline-${paddingInline}`]: !!paddingInline },
        { [`box-block-${paddingBlock}`]: !!paddingBlock },
        { [`box-block-start-${paddingBlockStart}`]: !!paddingBlockStart },
        { [`box-block-end-${paddingBlockEnd}`]: !!paddingBlockEnd },
        { [`box-inline-start-${paddingInlineStart}`]: !!paddingInlineStart },
        { [`box-inline-end-${paddingInlineEnd}`]: !!paddingInlineEnd },
    ]}
    {...rest}
>
    <slot />
</div>

<style></style>
