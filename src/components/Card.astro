---
// These are the overall layout cards that float over each other.
//
import type { BOX_PROPS } from "../types/box-props.type";
interface Props extends Omit<BOX_PROPS, "borderRadius"> {
    stackOrder?: "1" | "2";
}

import Box from "./Box.astro";

const {
    surface = null,
    padding = null,
    paddingBlock = null,
    paddingBlockStart = null,
    paddingBlockEnd = null,
    paddingInline = null,
    paddingInlineStart = null,
    paddingInlineEnd = null,
    class: className = "",
    stackOrder = null,
    ...rest
} = Astro.props;
---

<Box
    surface={surface}
    padding={padding}
    paddingBlock={paddingBlock}
    paddingBlockStart={paddingBlockStart}
    paddingBlockEnd={paddingBlockEnd}
    paddingInline={paddingInline}
    paddingInlineStart={paddingInlineStart}
    paddingInlineEnd={paddingInlineEnd}
    class={`${className} card`}
    {...{ "data-stack-order": stackOrder }}
    {...rest}
>
    <slot />
</Box>

<style is:global>
    .card {
        --card-border-radius: var(--cg-border-radius-400);
        --card-padding: var(--cg-space-600);

        position: relative;
        border-radius: var(--card-border-radius);
        padding: var(--card-padding);
    }

    @media (min-width: 768px) {
        .card {
            --card-padding: var(--cg-space-1000);
        }
    }

    .card[data-stack-order="1"] {
        z-index: 1;
    }
    .card[data-stack-order="2"] {
        z-index: 2;
    }
</style>
